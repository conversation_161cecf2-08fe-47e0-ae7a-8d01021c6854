#ifndef SCENE_H
#define SCENE_H

#define NOMINMAX
#include "object.h"
#include "light.h"
#include "triangle.h"
#include <vector>
#include <memory>
#include <limits>

class Scene {
public:
    std::vector<std::unique_ptr<Object>> objects;
    std::vector<Light> lights;
    Vec3 backgroundColor;
    
    Scene() : backgroundColor(Vec3(0.1, 0.1, 0.2)) {}
    
    void addTriangle(const Vec3& v0, const Vec3& v1, const Vec3& v2,
                     const Vec2& uv0, const Vec2& uv1, const Vec2& uv2,
                     const Material& material) {
        objects.push_back(std::make_unique<Triangle>(v0, v1, v2, uv0, uv1, uv2, material));
    }
    
    void addQuad(const Vec3& v0, const Vec3& v1, const Vec3& v2, const Vec3& v3,
                 const Vec2& uv0, const Vec2& uv1, const Vec2& uv2, const Vec2& uv3,
                 const Material& material) {
        objects.push_back(std::make_unique<Triangle>(v0, v1, v2, uv0, uv1, uv2, material));
        objects.push_back(std::make_unique<Triangle>(v0, v2, v3, uv0, uv2, uv3, material));
    }
    
    void addLight(const Light& light) {
        lights.push_back(light);
    }
    
    bool intersect(const Ray& ray, HitInfo& hitInfo) const {
        HitInfo closestHit;
        closestHit.t = std::numeric_limits<double>::max();
        bool foundHit = false;
        
        for (const auto& object : objects) {
            HitInfo currentHit;
            if (object->intersect(ray, currentHit) && currentHit.t < closestHit.t) {
                closestHit = currentHit;
                foundHit = true;
            }
        }
        
        if (foundHit) {
            hitInfo = closestHit;
            return true;
        }
        
        return false;
    }
};

#endif // SCENE_H
