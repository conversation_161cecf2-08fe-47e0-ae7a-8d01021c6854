#ifndef TRIANGLE_H
#define TRIANGLE_H

#include "object.h"
#include "vector.h"
#include "constants.h"
#include <cmath>

class Triangle : public Object {
public:
    Vec3 v0, v1, v2;       // Vertices
    Vec2 uv0, uv1, uv2;    // Texture coordinates per vertex
    Vec3 edge1, edge2;     // Edges for intersection calculation
    Vec3 faceNormal;       // Precomputed face normal

    Triangle(const Vec3& v0, const Vec3& v1, const Vec3& v2,
             const Vec2& uv0, const Vec2& uv1, const Vec2& uv2,
             const Material& material)
        : Object(material), v0(v0), v1(v1), v2(v2), uv0(uv0), uv1(uv1), uv2(uv2) {
        edge1 = v1 - v0;
        edge2 = v2 - v0;
        faceNormal = edge1.cross(edge2).normalize();
    }

    // Möller–Trumbore intersection algorithm
    bool intersect(const Ray& ray, HitInfo& hitInfo) const override {
        Vec3 pvec = ray.direction.cross(edge2);
        double det = edge1.dot(pvec);
        
        if (det < EPSILON) return false; // Back-face culling

        double invDet = 1.0 / det;
        Vec3 tvec = ray.origin - v0;
        double u = tvec.dot(pvec) * invDet; // Barycentric coord u
        if (u < -EPSILON || u > 1.0 + EPSILON) return false;

        Vec3 qvec = tvec.cross(edge1);
        double v = ray.direction.dot(qvec) * invDet; // Barycentric coord v
        if (v < -EPSILON || u + v > 1.0 + EPSILON) return false;

        double t = edge2.dot(qvec) * invDet; // Distance t

        if (t > EPSILON) {
            hitInfo.hit = true;
            hitInfo.t = t;
            hitInfo.point = ray.origin + ray.direction * t;
            hitInfo.normal = faceNormal;
            hitInfo.material = material;
            
            // Interpolate UV coordinates
            double w = 1.0 - u - v;
            hitInfo.uv.u = uv0.u * w + uv1.u * u + uv2.u * v;
            hitInfo.uv.v = uv0.v * w + uv1.v * u + uv2.v * v;
            
            return true;
        }
        return false;
    }

    AABB getAABB() const override {
        AABB box;
        box.expand(v0);
        box.expand(v1);
        box.expand(v2);
        const double padding = 1e-4;
        box.min = box.min - Vec3(padding, padding, padding);
        box.max = box.max + Vec3(padding, padding, padding);
        return box;
    }
};

#endif // TRIANGLE_H
