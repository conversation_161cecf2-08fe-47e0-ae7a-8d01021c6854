#ifndef CAMERA_H
#define CAMERA_H

#include "vector.h"
#include "ray.h"
#include <cmath>

class Camera {
public:
    Vec3 position;
    Vec3 target;
    Vec3 up;
    double fov;        // Field of view in radians
    double aspectRatio;
    double nearPlane;
    double farPlane;
    
    // Computed vectors
    Vec3 forward;
    Vec3 right;
    Vec3 upVector;
    
    Camera(const Vec3& pos = Vec3(0, 0, 5),
           const Vec3& tgt = Vec3(0, 0, 0),
           const Vec3& upDir = Vec3(0, 1, 0),
           double fieldOfView = 45.0 * PI / 180.0,
           double aspect = 16.0 / 9.0,
           double near = 0.1,
           double far = 1000.0)
        : position(pos), target(tgt), up(upDir), fov(fieldOfView),
          aspectRatio(aspect), nearPlane(near), farPlane(far) {
        updateVectors();
    }
    
    void updateVectors() {
        forward = (target - position).normalize();
        right = forward.cross(up).normalize();
        upVector = right.cross(forward).normalize();
    }
    
    void setPosition(const Vec3& pos) {
        position = pos;
        updateVectors();
    }
    
    void setTarget(const Vec3& tgt) {
        target = tgt;
        updateVectors();
    }
    
    void lookAt(const Vec3& pos, const Vec3& tgt, const Vec3& upDir) {
        position = pos;
        target = tgt;
        up = upDir;
        updateVectors();
    }
    
    // Generate a ray for screen coordinates (x, y) in range [0, 1]
    Ray getRay(double x, double y) const {
        // Convert screen coordinates to NDC [-1, 1]
        double ndcX = 2.0 * x - 1.0;
        double ndcY = 1.0 - 2.0 * y; // Flip Y axis
        
        // Calculate ray direction
        double tanHalfFov = std::tan(fov * 0.5);
        Vec3 rayDir = forward + 
                      right * (ndcX * tanHalfFov * aspectRatio) +
                      upVector * (ndcY * tanHalfFov);
        
        return Ray(position, rayDir.normalize());
    }
};

#endif // CAMERA_H
