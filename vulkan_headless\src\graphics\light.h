#ifndef LIGHT_H
#define LIGHT_H

#include "vector.h"

enum class LightType {
    DIRECTIONAL,
    POINT,
    SPOT
};

struct Light {
    LightType type;
    Vec3 position;
    Vec3 direction;
    Vec3 color;
    double intensity;
    double range;        // For point/spot lights
    double innerCone;    // For spot lights (in radians)
    double outerCone;    // For spot lights (in radians)
    
    // Directional light constructor
    Light(const Vec3& dir, const Vec3& col, double intens)
        : type(LightType::DIRECTIONAL), position(Vec3(0, 0, 0)), 
          direction(dir.normalize()), color(col), intensity(intens),
          range(0.0), innerCone(0.0), outerCone(0.0) {}
    
    // Point light constructor
    Light(const Vec3& pos, const Vec3& col, double intens, double r)
        : type(LightType::POINT), position(pos), direction(Vec3(0, -1, 0)),
          color(col), intensity(intens), range(r), innerCone(0.0), outerCone(0.0) {}
    
    // Spot light constructor
    Light(const Vec3& pos, const Vec3& dir, const Vec3& col, double intens, 
          double r, double inner, double outer)
        : type(LightType::SPOT), position(pos), direction(dir.normalize()),
          color(col), intensity(intens), range(r), innerCone(inner), outerCone(outer) {}
};

#endif // LIGHT_H
