#ifndef GRAPHICS_ENGINE_H
#define GRAPHICS_ENGINE_H

#include "scene.h"
#include "camera.h"
#include "raytracer.h"
#include "material.h"
#include "light.h"
#include <memory>
#include <string>
#include <fstream>
#include <limits>

class GraphicsEngine {
public:
    Scene scene;
    Camera camera;
    RayTracer raytracer;
    
    GraphicsEngine(int width = 800, int height = 600) {
        RayTracer::RenderSettings settings;
        settings.width = width;
        settings.height = height;
        settings.maxDepth = 5;
        settings.samples = 1;
        settings.enableShadows = true;
        settings.enableReflections = true;
        
        raytracer = RayTracer(settings);
        
        // Set up default camera
        camera.lookAt(Vec3(0, 2, 5), Vec3(0, 0, 0), Vec3(0, 1, 0));
        camera.aspectRatio = static_cast<double>(width) / height;
        
        setupDefaultScene();
    }
    
    void setupDefaultScene() {
        // Create materials
        Material redMaterial(Vec3(0.8, 0.2, 0.2), 0.1, 0.8, 0.3, 32.0, 0.1);
        Material greenMaterial(Vec3(0.2, 0.8, 0.2), 0.1, 0.8, 0.3, 32.0, 0.1);
        Material blueMaterial(Vec3(0.2, 0.2, 0.8), 0.1, 0.8, 0.3, 32.0, 0.1);
        Material floorMaterial(Vec3(0.7, 0.7, 0.7), 0.2, 0.8, 0.1, 16.0, 0.0);
        
        // Add floor
        scene.addQuad(
            Vec3(-5, -1, -5), Vec3(5, -1, -5), Vec3(5, -1, 5), Vec3(-5, -1, 5),
            Vec2(0, 0), Vec2(1, 0), Vec2(1, 1), Vec2(0, 1),
            floorMaterial
        );
        
        // Add some triangles/objects
        scene.addTriangle(
            Vec3(-1, 0, 0), Vec3(1, 0, 0), Vec3(0, 2, 0),
            Vec2(0, 0), Vec2(1, 0), Vec2(0.5, 1),
            redMaterial
        );
        
        scene.addTriangle(
            Vec3(2, 0, -1), Vec3(3, 0, 1), Vec3(2.5, 1.5, 0),
            Vec2(0, 0), Vec2(1, 0), Vec2(0.5, 1),
            greenMaterial
        );
        
        scene.addTriangle(
            Vec3(-3, 0, 0), Vec3(-2, 0, -1), Vec3(-2.5, 1, 0.5),
            Vec2(0, 0), Vec2(1, 0), Vec2(0.5, 1),
            blueMaterial
        );
        
        // Add lights
        scene.addLight(Light(Vec3(0, 1, 0), Vec3(1, 1, 1), 0.8)); // Directional light
        scene.addLight(Light(Vec3(3, 3, 3), Vec3(1, 0.8, 0.6), 1.0, 10.0)); // Point light
    }
    
    std::vector<Vec3> render() {
        return raytracer.render(scene, camera);
    }
    
    void setCamera(const Vec3& position, const Vec3& target, const Vec3& up = Vec3(0, 1, 0)) {
        camera.lookAt(position, target, up);
    }
    
    void addTriangle(const Vec3& v0, const Vec3& v1, const Vec3& v2, const Material& material) {
        scene.addTriangle(v0, v1, v2, Vec2(0, 0), Vec2(1, 0), Vec2(0.5, 1), material);
    }
    
    void addLight(const Light& light) {
        scene.addLight(light);
    }
    
    void clearScene() {
        scene.objects.clear();
        scene.lights.clear();
    }
    
    // Save rendered image as PPM format
    void saveImage(const std::vector<Vec3>& colorBuffer, const std::string& filename) {
        std::ofstream file(filename);
        file << "P3\n" << raytracer.settings.width << " " << raytracer.settings.height << "\n255\n";
        
        for (const Vec3& color : colorBuffer) {
            int r = static_cast<int>(color.x * 255);
            int g = static_cast<int>(color.y * 255);
            int b = static_cast<int>(color.z * 255);
            file << r << " " << g << " " << b << "\n";
        }
    }
};

#endif // GRAPHICS_ENGINE_H
