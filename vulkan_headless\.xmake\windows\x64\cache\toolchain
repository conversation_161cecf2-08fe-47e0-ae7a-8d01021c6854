{
    go_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        __global = true,
        arch = "x64"
    },
    msvc_arch_x64_plat_windows = {
        vcarchs = {
            "arm",
            "arm64",
            "arm64ec",
            "x64",
            "x86"
        },
        vs_sdkver = "10.0.19041.0",
        arch = "x64",
        vs = "2022",
        vcvars = {
            LIB = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\ATLMFC\lib\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\lib\x64;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\lib\um\x64;C:\Program Files (x86)\Windows Kits\10\lib\10.0.19041.0\ucrt\x64;C:\Program Files (x86)\Windows Kits\10\\lib\10.0.19041.0\\um\x64]],
            VCInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\]],
            PATH = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\VC\VCPackages;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\TestWindow;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\TeamFoundation\Team Explorer;C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\bin\Roslyn;C:\Program Files (x86)\Microsoft SDKs\Windows\v10.0A\bin\NETFX 4.8 Tools\x64\;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\FSharp\Tools;C:\Program Files\Microsoft Visual Studio\2022\Community\Team Tools\DiagnosticsHub\Collector;C:\Program Files (x86)\Windows Kits\10\bin\10.0.19041.0\\x64;C:\Program Files (x86)\Windows Kits\10\bin\\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\\MSBuild\Current\Bin\amd64;C:\Windows\Microsoft.NET\Framework64\v4.0.30319;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\;C:\VulkanSDK\1.3.296.0\Bin;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jre-8.0.412.8-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-8.0.412.8-hotspot\bin;C:\Python311\Scripts\;C:\Python311\;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\redist\intel64_win\compiler;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\Autodesk\Backburner\;C:\Program Files (x86)\Common Files\Autodesk Shared\;C:\Program Files (x86)\dotnet\;C:\Program Files\dotnet\;C:\ProgramData\chocolatey\bin;C:\Program Files\nodejs\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\Desktop\msys\mingw64\bin;C:\Program Files\Meson\;C:\Program Files\Git\cmd;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\xmake;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\Ninja;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\VC\Linux\bin\ConnectionManagerExe;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\vcpkg]],
            VS170COMNTOOLS = [[C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\]],
            VCIDEInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\VC\]],
            LIBPATH = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\ATLMFC\lib\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\lib\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\lib\x86\store\references;C:\Program Files (x86)\Windows Kits\10\UnionMetadata\10.0.19041.0;C:\Program Files (x86)\Windows Kits\10\References\10.0.19041.0;C:\Windows\Microsoft.NET\Framework64\v4.0.30319]],
            UniversalCRTSdkDir = [[C:\Program Files (x86)\Windows Kits\10\]],
            ExtensionSdkDir = [[C:\Program Files (x86)\Microsoft SDKs\Windows Kits\10\ExtensionSDKs]],
            DevEnvdir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\]],
            WindowsSdkDir = [[C:\Program Files (x86)\Windows Kits\10\]],
            UCRTVersion = "10.0.19041.0",
            VSCMD_ARG_app_plat = "Desktop",
            VCToolsRedistDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Redist\MSVC\14.42.34433\]],
            VisualStudioVersion = "17.0",
            VSCMD_ARG_TGT_ARCH = "x64",
            VSInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\]],
            WindowsSDKVersion = "10.0.19041.0",
            VCToolsVersion = "14.43.34808",
            WindowsLibPath = [[C:\Program Files (x86)\Windows Kits\10\UnionMetadata\10.0.19041.0;C:\Program Files (x86)\Windows Kits\10\References\10.0.19041.0]],
            INCLUDE = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\ATLMFC\include;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.19041.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.19041.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.19041.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.19041.0\\cppwinrt;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um]],
            VSCMD_ARG_HOST_ARCH = "x64",
            VSCMD_VER = "17.13.1",
            WindowsSdkBinPath = [[C:\Program Files (x86)\Windows Kits\10\bin\]],
            VCToolsInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\]],
            WindowsSdkVerBinPath = [[C:\Program Files (x86)\Windows Kits\10\bin\10.0.19041.0\]]
        },
        __checked = "2022",
        plat = "windows",
        __global = true,
        vs_toolset = "14.43.34808"
    },
    swift_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        __global = true,
        arch = "x64"
    },
    rust_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        __global = true,
        arch = "x64"
    },
    tool_target_vulkan_headless_windows_x64_ld = {
        toolchain_info = {
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc",
            arch = "x64"
        },
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\link.exe]],
        toolname = "link"
    },
    gfortran_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        __global = true,
        arch = "x64"
    },
    tool_target_vulkan_headless_windows_x64_cc = {
        toolchain_info = {
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc",
            arch = "x64"
        },
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\cl.exe]],
        toolname = "cl"
    },
    fpc_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        __global = true,
        arch = "x64"
    },
    cuda_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        __global = true,
        arch = "x64"
    },
    tool_target_vulkan_headless_windows_x64_cxx = {
        toolchain_info = {
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc",
            arch = "x64"
        },
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\cl.exe]],
        toolname = "cl"
    },
    nasm_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        __global = true,
        arch = "x64"
    },
    yasm_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        __global = true,
        arch = "x64"
    },
    nim_arch_x64_plat_windows = {
        __checked = false,
        plat = "windows",
        __global = true,
        arch = "x64"
    }
}