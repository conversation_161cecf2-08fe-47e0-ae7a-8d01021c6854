#ifndef OBJECT_H
#define OBJECT_H

#include "vector.h"
#include "ray.h"
#include "aabb.h"
#include "material.h"

struct HitInfo {
    bool hit;
    double t;
    Vec3 point;
    Vec3 normal;
    Vec2 uv;
    Material material;
    
    HitInfo() : hit(false), t(0.0) {}
};

// Abstract Base Class for Objects
class Object {
public:
    Material material;
    
    Object(const Material& material) : material(material) {}
    virtual ~Object() = default;
    
    virtual bool intersect(const Ray& ray, HitInfo& hitInfo) const = 0;
    virtual AABB getAABB() const = 0;
};

#endif // OBJECT_H
