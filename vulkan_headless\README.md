# Vulkan Headless 3D Graphics Project

A headless Vulkan application built with xmake for 3D graphics computation without a window or display.

## Prerequisites

- **Vulkan SDK**: Installed at `E:\VulkanSDK`
- **xmake**: Build system
- **Visual Studio 2022**: C++ compiler (or compatible)

## Project Structure

```
vulkan_headless/
├── src/
│   └── main.cpp          # Main Vulkan headless application
├── xmake.lua             # Build configuration
└── README.md             # This file
```

## Building

### Release Build (Recommended)
```bash
cd vulkan_headless
xmake f -m release
xmake
```

### Debug Build (with validation layers)
```bash
cd vulkan_headless
xmake f -m debug
xmake
```

**Note**: Debug build requires Vulkan validation layers to be properly installed. If you encounter issues with the debug build, use the release build instead.

## Running

```bash
xmake run
```

Or run the executable directly:
```bash
# Release
.\build\windows\x64\release\vulkan_headless.exe

# Debug
.\build\windows\x64\debug\vulkan_headless.exe
```

## Features

- **Headless Vulkan Context**: Initializes Vulkan without requiring a window
- **GPU Detection**: Automatically selects a suitable GPU with compute capabilities
- **Validation Layers**: Enabled in debug builds for development
- **Cross-platform**: Configured for Windows with potential Linux support

## Output

When running successfully, you should see:
```
Selected GPU: [Your GPU Name]
Vulkan headless context initialized successfully!
```

## Configuration

The project is configured to:
- Use C++17 standard
- Link against Vulkan SDK at `E:\VulkanSDK`
- Enable validation layers in debug mode
- Support compute queue operations

## Next Steps

This basic setup provides a foundation for:
- Compute shaders
- 3D rendering to offscreen buffers
- GPU-accelerated calculations
- Vulkan learning and experimentation

## Troubleshooting

- **"Vulkan SDK not found"**: Ensure Vulkan SDK is installed at `E:\VulkanSDK`
- **"Failed to create instance"**: In debug mode, validation layers might not be available
- **Compilation errors**: Ensure Visual Studio 2022 and xmake are properly installed
