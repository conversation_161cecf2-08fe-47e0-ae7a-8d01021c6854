#ifndef AABB_H
#define AABB_H

#define NOMINMAX
#include "vector.h"
#include "ray.h"
#include <algorithm>
#include <limits>

struct AABB {
    Vec3 min, max;
    
    AABB() : min(Vec3(std::numeric_limits<double>::max(), 
                      std::numeric_limits<double>::max(), 
                      std::numeric_limits<double>::max())),
             max(Vec3(std::numeric_limits<double>::lowest(), 
                      std::numeric_limits<double>::lowest(), 
                      std::numeric_limits<double>::lowest())) {}
    
    AABB(const Vec3& min, const Vec3& max) : min(min), max(max) {}
    
    void expand(const Vec3& point) {
        min.x = std::min(min.x, point.x);
        min.y = std::min(min.y, point.y);
        min.z = std::min(min.z, point.z);
        max.x = std::max(max.x, point.x);
        max.y = std::max(max.y, point.y);
        max.z = std::max(max.z, point.z);
    }
    
    void expand(const AABB& other) {
        expand(other.min);
        expand(other.max);
    }
    
    Vec3 center() const {
        return (min + max) * 0.5;
    }
    
    Vec3 size() const {
        return max - min;
    }
    
    double surfaceArea() const {
        Vec3 d = size();
        return 2.0 * (d.x * d.y + d.y * d.z + d.z * d.x);
    }
    
    bool intersect(const Ray& ray, double& tMin, double& tMax) const {
        Vec3 invDir = Vec3(1.0 / ray.direction.x, 1.0 / ray.direction.y, 1.0 / ray.direction.z);
        
        Vec3 t1 = Vec3((min.x - ray.origin.x) * invDir.x,
                       (min.y - ray.origin.y) * invDir.y,
                       (min.z - ray.origin.z) * invDir.z);
        Vec3 t2 = Vec3((max.x - ray.origin.x) * invDir.x,
                       (max.y - ray.origin.y) * invDir.y,
                       (max.z - ray.origin.z) * invDir.z);
        
        Vec3 tNear = Vec3(std::min(t1.x, t2.x), std::min(t1.y, t2.y), std::min(t1.z, t2.z));
        Vec3 tFar = Vec3(std::max(t1.x, t2.x), std::max(t1.y, t2.y), std::max(t1.z, t2.z));
        
        tMin = std::max({tNear.x, tNear.y, tNear.z});
        tMax = std::min({tFar.x, tFar.y, tFar.z});
        
        return tMax >= tMin && tMax >= 0.0;
    }
};

#endif // AABB_H
