#ifndef MATERIAL_H
#define MATERIAL_H

#include "vector.h"
#include <string>

class Material {
public:
    std::string name;
    Vec3 color;
    double ambient;
    double diffuse;
    double specular;
    double shininess;
    double reflectivity;
    double transparency;
    double refractiveIndex;

    // Constructor
    Material(const Vec3& col = Vec3(0.5, 0.5, 0.5), 
             double amb = 0.1, 
             double diff = 0.8, 
             double spec = 0.0, 
             double shin = 1.0,
             double refl = 0.0,
             double trans = 0.0,
             double refr = 1.0)
        : color(col), ambient(amb), diffuse(diff), specular(spec), 
          shininess(shin), reflectivity(refl), transparency(trans), 
          refractiveIndex(refr) {}
};

#endif // MATERIAL_H
