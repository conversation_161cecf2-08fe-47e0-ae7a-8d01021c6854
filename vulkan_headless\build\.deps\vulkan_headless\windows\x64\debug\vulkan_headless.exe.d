{
    values = {
        [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\link.exe]],
        {
            "-nologo",
            "-dynamicbase",
            "-nxcompat",
            "-machine:x64",
            [[-libpath:E:\VulkanSDK\Lib]],
            "-debug",
            [[-pdb:build\windows\x64\debug\vulkan_headless.pdb]],
            "vulkan-1.lib",
            "user32.lib",
            "gdi32.lib"
        }
    },
    files = {
        [[build\.objs\vulkan_headless\windows\x64\debug\src\main.cpp.obj]]
    }
}