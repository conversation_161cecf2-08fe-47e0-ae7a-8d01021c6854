{
    ["detect.sdks.find_vcpkgdir"] = false,
    find_package_windows_x64 = {
        ["pkgconfig::vulkan_release"] = false
    },
    find_programver = {
        ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\cl.exe"] = "19.43.34808"
    },
    find_program_msvc_arch_x64_plat_windows_checktoolcxx = {
        ["cl.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\cl.exe]]
    },
    find_program_msvc_arch_x64_plat_windows_checktoolcc = {
        ["cl.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\cl.exe]]
    },
    find_package_windows_x64_fetch_package_system = {
        vulkansdk_fe796fc564ce40269511110758524345_release_external = false
    },
    find_program = {
        pkgconf = false,
        ["cl.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\cl.exe]],
        git = [[C:\Users\<USER>\AppData\Local\.xmake\packages\g\git\2.20.0\65b10f9e7885425fbf2d2abaad2a61a9\share\MinGit\cmd\git.exe]],
        ["pkg-config"] = false,
        nim = false,
        ["7z"] = [[C:\Program Files\xmake\winenv\bin\7z]],
        gzip = false
    },
    find_program_msvc_arch_x64_plat_windows_checktoolld = {
        ["link.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\link.exe]]
    },
    find_package_windows_x64_fetch_package_xmake = {
        ["xmake::vulkansdk_fe796fc564ce40269511110758524345_release_latest_external"] = false
    }
}