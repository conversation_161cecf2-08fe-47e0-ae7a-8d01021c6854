#ifndef RAYTRACER_H
#define RAYTRACER_H

#define NOMINMAX
#include "scene.h"
#include "camera.h"
#include "vector.h"
#include "ray.h"
#include "constants.h"
#include <vector>
#include <algorithm>

class RayTracer {
public:
    struct RenderSettings {
        int width = 800;
        int height = 600;
        int maxDepth = 5;
        int samples = 1;
        bool enableShadows = true;
        bool enableReflections = true;
        double gamma = 2.2;
    };
    
    RenderSettings settings;
    
    RayTracer(const RenderSettings& renderSettings = RenderSettings()) 
        : settings(renderSettings) {}
    
    // Render the scene to a color buffer
    std::vector<Vec3> render(const Scene& scene, const Camera& camera) {
        std::vector<Vec3> colorBuffer(settings.width * settings.height);
        
        for (int y = 0; y < settings.height; ++y) {
            for (int x = 0; x < settings.width; ++x) {
                Vec3 color(0, 0, 0);
                
                // Multi-sampling for anti-aliasing
                for (int sample = 0; sample < settings.samples; ++sample) {
                    double u = (x + 0.5) / settings.width;
                    double v = (y + 0.5) / settings.height;
                    
                    Ray ray = camera.getRay(u, v);
                    color = color + traceRay(ray, scene, 0);
                }
                
                color = color / static_cast<double>(settings.samples);
                
                // Gamma correction
                color.x = std::pow(color.x, 1.0 / settings.gamma);
                color.y = std::pow(color.y, 1.0 / settings.gamma);
                color.z = std::pow(color.z, 1.0 / settings.gamma);
                
                // Clamp values
                color.x = std::max(0.0, std::min(1.0, color.x));
                color.y = std::max(0.0, std::min(1.0, color.y));
                color.z = std::max(0.0, std::min(1.0, color.z));
                
                colorBuffer[y * settings.width + x] = color;
            }
        }
        
        return colorBuffer;
    }
    
private:
    Vec3 traceRay(const Ray& ray, const Scene& scene, int depth) {
        if (depth >= settings.maxDepth) {
            return Vec3(0, 0, 0);
        }
        
        HitInfo hitInfo;
        if (!scene.intersect(ray, hitInfo)) {
            return scene.backgroundColor;
        }
        
        Vec3 color = calculateLighting(hitInfo, scene, ray);
        
        // Handle reflections
        if (settings.enableReflections && hitInfo.material.reflectivity > 0.0) {
            Vec3 reflectDir = reflect(ray.direction, hitInfo.normal);
            Ray reflectRay(hitInfo.point + hitInfo.normal * EPSILON, reflectDir);
            Vec3 reflectColor = traceRay(reflectRay, scene, depth + 1);
            color = color * (1.0 - hitInfo.material.reflectivity) + 
                    reflectColor * hitInfo.material.reflectivity;
        }
        
        return color;
    }
    
    Vec3 calculateLighting(const HitInfo& hitInfo, const Scene& scene, const Ray& ray) {
        Vec3 color = hitInfo.material.color * hitInfo.material.ambient;
        
        for (const Light& light : scene.lights) {
            Vec3 lightDir;
            double lightDistance;
            double attenuation = 1.0;
            
            if (light.type == LightType::DIRECTIONAL) {
                lightDir = -light.direction;
                lightDistance = std::numeric_limits<double>::max();
            } else {
                Vec3 lightVec = light.position - hitInfo.point;
                lightDistance = lightVec.magnitude();
                lightDir = lightVec / lightDistance;
                
                if (light.type == LightType::POINT) {
                    attenuation = 1.0 / (1.0 + 0.1 * lightDistance + 0.01 * lightDistance * lightDistance);
                }
            }
            
            // Shadow test
            if (settings.enableShadows && isInShadow(hitInfo.point, lightDir, lightDistance, scene)) {
                continue;
            }
            
            // Diffuse lighting
            double diffuseFactor = std::max(0.0, hitInfo.normal.dot(lightDir));
            Vec3 diffuse = hitInfo.material.color * hitInfo.material.diffuse * 
                          light.color * light.intensity * diffuseFactor * attenuation;
            
            // Specular lighting
            Vec3 reflectDir = reflect(-lightDir, hitInfo.normal);
            Vec3 viewDir = -ray.direction;
            double specularFactor = std::pow(std::max(0.0, viewDir.dot(reflectDir)), hitInfo.material.shininess);
            Vec3 specular = light.color * hitInfo.material.specular * light.intensity * 
                           specularFactor * attenuation;
            
            color = color + diffuse + specular;
        }
        
        return color;
    }
    
    bool isInShadow(const Vec3& point, const Vec3& lightDir, double lightDistance, const Scene& scene) {
        Ray shadowRay(point + lightDir * EPSILON, lightDir);
        HitInfo shadowHit;
        
        if (scene.intersect(shadowRay, shadowHit)) {
            return shadowHit.t < lightDistance - EPSILON;
        }
        
        return false;
    }
    
    Vec3 reflect(const Vec3& incident, const Vec3& normal) {
        return incident - normal * 2.0 * incident.dot(normal);
    }
};

#endif // RAYTRACER_H
